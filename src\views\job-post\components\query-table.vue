<script lang="ts" setup>
import { getCurrentInstance } from 'vue';

import type { ClassMgtListItemType } from '^/types/class-management';

import TableSelectable from '@/components/common/table-selectable';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';

import { isY } from '@/helps/y-o-n';
import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';

import { createColumns } from '../helps/columns';

defineProps<{
  isCheckedAll: boolean;
  data: ClassMgtListItemType[];
  loading: boolean;
}>();

const emit = defineEmits<{
  'on-edit': [id:number]
  'on-set-graduate': [id:number]
  'on-delete': [id:number]
}>();

const selectedIds = defineModel<ClassMgtListItemType[]>('selected');

const vm = getCurrentInstance();
const t = namespaceT('jobPost');
const td = namespaceT('dateFormat');
const columns = createColumns();

const onEdit = (row:ClassMgtListItemType) => {
  emit('on-edit', row.id);
};

const onReapply = (row:ClassMgtListItemType) => {
  emit('on-reapply', row.id);
};

const onDelete = (row: ClassMgtListItemType) => {
  emit('on-delete', row.id);
};

const can = (type: string) => {
  return vm.proxy.$can((P) => P.ClassManagement[type]);
};


const actions = [
  {
    label: t('action.edit'),
    triggerEvent: onEdit,
    can: (row) => isY(row.editableInd) && can('Edit'),
  },
  {
    label: t('action.setGraduate'),
    triggerEvent: onSetGraduate,
    can: (row) => isY(row.gradableInd) && can('SetGraduate'),
  },
  {
    label: t('action.delete'),
    triggerEvent: onDelete,
    can: (row) => isY(row.deletableInd),
  },
];


const formatToDateTime = (date: Date | string) => {
  return dateFormatSTZ(new Date(date), td('dateTime'));
};


</script>


<template>
  <TableSelectable
    :is-checked-all="isCheckedAll"
    :columns="columns"
    :data="data"
    :loading="loading"
    :selected="selectedIds"
    @update:selected="onUpdateSelected"
  >
    <!-- 班导师 -->
    <template #tutor="{ row }">
      {{ getTutorNameText(row.clsSupervisorNameMulti) }}
    </template>

    <!-- 班级状态 -->
    <template #situation="{ row }">
      {{ classStatusStore.getTextByCode(row.clsStatus) }}
    </template>

    <!-- 创建时间 -->
    <template #createTime="{ row }">
      {{ formatToDateTime(row.creTime) }}
    </template>

    <!-- 操作人/时间 -->
    <template #operatorAndTime="{ row }">
      {{ row.updName }}
      <br>
      {{ formatToDateTime(row.updTime) }}
    </template>


    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          :row-data="row"
          :limit="3"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </TableSelectable>
</template>
