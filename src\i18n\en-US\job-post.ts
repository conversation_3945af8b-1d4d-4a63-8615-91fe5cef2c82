export default {
  label: {
    status: '状态',
    publishStatus: '发布状态',
    createTime: '创建时间',

    postName: '岗位名称',
    postType: '岗位类型',
    researchDirection: '研究方向',
    sexRemand: '性别要求',
    neededMajor: '所需专业',
    numOfPerson: '需求人数',
    jobContent: '工作内容',
    practiceAddress: '实践地址',
    detailedAddress: '详细地址',
    deadline: '报名截止时间',
    otherRemand: '其他要求',
    publishType: '发布类型',

    postUnit: '岗位单位',
    companyProfile: '公司简介',
  },

  columns: {
    postName: '@:jobPose.label.postName',
    researchDirection: '@:jobPose.label.researchDirection',
    sexRemand: '@:jobPose.label.sexRemand',
    neededMajor: '@:jobPose.label.neededMajor',
    numOfPerson: '@:jobPose.label.numOfPerson',
    practiceAddress: '@:jobPose.label.practiceAddress',
    deadline: '@:jobPose.label.deadline',
    createTime: '@:jobPose.label.createTime',
    status: '@:jobPose.label.status',
    publishStatusAndTime: '发布状态/发布时间',

  },

  action: {
    edit: '@:common.action.edit',
    delete: '@:common.action.delete',
    export: '@:common.action.export',
    cancel: '@:common.action.cancel',

    add: '新增岗位',
    view: '查看',
    reapply: '重新发起',
    draft: '暂存草稿',
    submit: '提交',
  },

};
