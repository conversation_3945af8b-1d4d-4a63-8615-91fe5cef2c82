<script setup lang="ts">
import { FacCLsListVO } from '@/uses/use-fac-cls';


interface Props {
  options?: FacCLsListVO[];
}

withDefaults(defineProps<Props>(), {
  options: () => [],
});


</script>


<template>
  <Select
    class="pima-select"
    v-bind="$attrs"
  >
    <Option
      v-for="item in options"
      :key="item.id"
      :value="item.id"
      :label="item.name"
    />
  </Select>
</template>
