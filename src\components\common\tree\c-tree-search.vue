<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import CTreeFilter from './c-tree-filter';


const props = withDefaults(defineProps<{
  showSearchBar?: boolean;
  loading?: boolean;
  keyword?: string;
  placeholder?: string;
}>(), {
  showSearchBar: false,
  loading: false,
  keyword: '',
  placeholder: '',
});

interface EmitType {
  (e: 'update:keyword', value: string): void;
}
const emit = defineEmits<EmitType>();

const kw = ref(props.keyword);
const { t } = useI18n();

watch(
  () => kw.value,
  (value) => {
    emit('update:keyword', value);
  },
);
</script>


<template>
  <div
    :class="{
      'pima-tree-wrap': true,
      'c-tree-wrap': true,
      'c-tree-wrap-has-search': showSearchBar,
    }"
  >
    <div
      v-show="showSearchBar"
      class="pima-tree-search"
    >
      <Input
        v-model.trim="kw"
        :placeholder="placeholder || t('common.placeholder.keyword')"
        class="pima-input-search-inline"
        search
        enter-button
        clearable
      />
    </div>

    <div class="c-tree-body">
      <CTreeFilter
        v-bind="$attrs"
        :keyword="kw"
      />
    </div>

    <Spin
      v-if="loading"
      class="pima-spin"
      fix
    />
  </div>
</template>


<style lang="less" scoped>
@calcHeight: calc(100vh - var(--header-height) - 50px);

.c-tree-wrap {
  height: @calcHeight;
  overflow: hidden;

  .c-tree-body {
    height: @calcHeight;
    overflow: hidden auto;
  }

  &.c-tree-wrap-has-search {
    .c-tree-body {
      height: calc(@calcHeight - var(--header-height));
    }
  }
}
</style>
