import type { RouteRecordRaw } from 'vue-router';
import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName as RN } from '@/config/router';
import { SiderMenuCodes as SMC } from '@/config/sider-menu';
import { Auth } from '@/config/auth';

import TheRoot from '@/components/the-root.vue';


export const routes = Object.freeze<RouteRecordRaw[]>([
  {
    path: PUBLIC_PATH,
    name: RN.Root,
    component: TheRoot,
    children: [

      // /** 学生资料 */
      // {
      //   path: 'student-info',
      //   name: RN.StudentInfo,
      //   component: () => import('@/views/student-info'),
      //   meta: {
      //     requiresAuth: true, // 不设置或设置为true需要认证
      //     activeCodes: [SMC.StudentInfo],
      //     baseAuthCodes: [Auth.StudentInfo.View],
      //   },
      // },


      {
        path: 'forbidden',
        name: RN.Forbidden,
        component: () => import('@/views/forbidden.vue'),
      },
    ],
  },
]);
