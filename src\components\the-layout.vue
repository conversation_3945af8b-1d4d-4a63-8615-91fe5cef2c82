<script lang='ts' setup>
interface Props {
  hasSider: boolean;
}

withDefaults(defineProps<Props>(), {
  hasSider: true,
});
</script>


<template>
  <div class="pima-layout">
    <div class="header">
      <slot name="header" />
    </div>
    <div
      class="body"
      :class="{ 'body-has-sider': hasSider }"
    >
      <div class="sider">
        <slot name="sider" />
      </div>
      <div class="main">
        <slot name="main" />
      </div>
    </div>
  </div>
</template>
