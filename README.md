# sigs-student-‌records‌-nodejs

SIGS / 学工系统 / 学生档案

## 安装

### 编译依赖

这个项目使用 [node](https://nodejs.org/) 和 [yarn](https://yarnpkg.com/)。请确保你本地安装了它们。

- node 需要 [16.18.x](https://nodejs.org/en/download/) 或以上TLS版本（v17.x及以上未测试，不建议使用）
- yarn 需要 [1.22.x](https://yarnpkg.com/getting-started/install) 或以上版本

### 服务器依赖

1. 运行系统

   在 CentOS 下运行 node v16 版本，需要手动安装特定工具，具体安装命令如下：

   `yum install centos-release-scl`

   `yum install devtoolset-7-gcc*`

   使用安装工具生效

   `source /opt/rh/devtoolset-7/enable` 或者 `scl enable devtoolset-7 bash`
2. Redis 服务器

   服务运行时需使用 Redis 相关服务，请确保已准备。
3. 外部依赖服务项

   服务运行时会访问以下相关的外部依服资源，请务必先拿到相关信息，具体如下表所示：


| 依赖服务         | 项目名称                                                                  | 备注                                                                                                 |
| ---------------- | ------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------- |
| CAS              |                                                                           | 统一身份认证                                                                                         |
| 工作流接口       |                                                                           | 工作流业务数据交互                                                                                   |
| 统一数据接口     |                                                                           | 工作流业务数据交互                                                                                   |
| 统一权限接口     |                                                                           | 工作流业务数据交互                                                                                   |
| 公共接口         |                                                                           | 申请/刷新 Token 数据交互                                                                             |
| 静态资源         |                                                                           | 前端静态资源，如：主题样式CSS、UEditor 富文本编辑器资源                                              |
| 远程 UI 入口文件 | [pima-remote-ui-nodejs](http://git.doocom.net/pima/pima-remote-ui-nodejs) | 前端公共 UI 资源，引入远程公用的头部/侧边栏/选人组件/导入组件/禁用组件 UI，Webpack Module Federation |

## 运行示例（测试/开发使用）

1. 克隆代码，执行命令 ```<NAME_EMAIL>:sigs/sigs-student-‌records‌-nodejs.git```
2. 进入工程文件夹，执行命令 ```cd sigs-student-‌records‌-nodejs```
3. 安装项目依赖，执行命令 ```yarn install --production=false```
4. 配置 [.env](#.env配置文件说明) 配置项
5. 启动示例，执行命令 ```yarn run dev```

## 构建（生产用）

1. 克隆代码，执行命令 ```<NAME_EMAIL>:sigs/sigs-student-‌records‌-nodejs.git```
2. 进入工程文件夹，执行命令 ```cd sigs-student-‌records‌-nodejs```
3. 安装项目 Webpack 依赖，执行命令 ```yarn install --production=true```
4. 配置 [.env](#.env配置文件说明) 配置项
5. Webpack 构建，执行命令 ```yarn run build```

## 部署

### 复制环境生产使用文件

生产环境下，只需要使用部分文件夹和文件，具体如下：


| 路径                | 备注                 |
| ------------------- | -------------------- |
| build/              |                      |
| dist/               |                      |
| node_modules/       |                      |
| server/             |                      |
| .env                | 非必要，环境变量配置 |
| ecosystem.config.js | 非必要，PM2 配置文件 |
| index.html          |                      |
| package.json        |                      |

### 在生产环境启动项目

- 使用 PM2 进程管理工具启动

  建议使用 [PM2](https://pm2.keymetrics.io/) 做为 node 进程管理工具（非必须），项目带有文件名为 ecosystem.config.js 的默认 PM2 配置文件，可直接用于启动。

  - PM2需要 [3.x](https://pm2.keymetrics.io/docs/usage/quick-start/) 或以上版本

  执行命令 ```pm2 start ./ecosystem.config.js --env production```
- 直接启动

  执行命令 ```yarn run preview```

### 配置 nginx

建议项，非必要。

由于构建完后，会生成 dist/static/ 文件夹，该文件夹内容为静态访问资源，通过 nginx 配置，可减少 node 端负载。

找到 nginx 配置文件，具体修改如下：

```
server {
  ...

  location /static/ { /* 静态资源访问路径 */
    alias /home/<USER>/sigs-student-‌records‌-nodejs/dist/static/; /* 构建输出dist⽂件夹下的静态资源文件夹路径 */
  }
}
```

## .env配置文件说明

项目运行前需要配置 .env 文件，.env 文件不存在项目的版本管理系统(git)当中，需要单独创建，.env 配置文件需存放在项目根目录中。

项目提供配置对照文件 .env.sample，可复制该文件创建 .env 文件。

请注意，提供 .env 配置文件的目的，是为了不能修改直接系统环境变量的情况下，做为补充的配置手段。如果你已经在环境变量配置对应的项，则可以不用创建 .env 文件。

### 项目 .env 文件所需的配置选项


| 键值                   | 备注                               | 必填 | 默认值                          | 示例                     | 构建时使用 | 说明                                                                                | 最后修改时间 |
| ---------------------- | ---------------------------------- | ---- | ------------------------------- | ------------------------ | ---------- | ----------------------------------------------------------------------------------- | ------------ |
| EXPRESS_PORT           | Express 运行端口号                 |      | 8080                            | 8080                     |            |                                                                                     |              |
| SERVICE_URL            | 外网服务访问 URL                   |      | http://localhost:{EXPRESS_PORT} | https://example.com:8080 |            | 不包含根目录如果该值没有填写，将默认值为 http://localhost: 加上 EXPRESS_PORT 配置值 |              |
| PUBLIC_PATH            | 根目录                             |      | /                               | /test-folder/            | ✓         | 与 SERVICE_URL 组成完整的访问路径，如：http://example.com:8080/test-folder/         |              |
| SERVICE_CODE           | 服务编码                           | ✓   |                                 | pima-project-template    | ✓         | 数据中心应用管理对应配置                                                            |              |
| DASHBOARD_SERVICE_CODE                   | 桌面服务编码                            | ✓   |                                 | pima-project-template   | ✓        | 数据中心应用管理对应配置                                                            |                     |
| USER_INFO_SERVICE_CODE | 用户信息服务编码                   | ✓   |                                 | pima-project-template    | ✓         | 数据中心应用管理对应配置                                                            |              |
| CLIENT_ID              | 客户端 ID                          | ✓   |                                 |                          |            | 数据中心客户端对应配置                                                              |              |
| CLIENT_SECRET          | 客户端密钥                         | ✓   |                                 |                          |            | 数据中心客户端对应配置                                                              |              |
| SESSION_ID_COOKIE_KEY  | 用于储存 Session ID 的 Cookie 键值 |      | sigs.double-selection-enterprise.sid          |                          |            | 本应用使用                                                                          |              |
| LOCALE_COOKIE_KEY      | 用于储存多语言的 Cookie 键值       |      | pima.locale                     |                          |            | 全局使用，建议全局配置一致                                                          |              |
| SUPPORT_LOCALES        | 支持语言                           |      | zh_CN,zh_HK,en_US               | zh_CN                    | ✓         | 不填写默认所有支持的语言，支持多个，使用英文逗号分隔                                |              |
| FALLBACK_LOCALE        | 默认语言                           |      | zh_CN                           | zh_CN                    | ✓         |                                                                                     |              |

#### 接口相关配置


| 键值                        | 备注                     | 必填 | 默认值 | 示例                                                       | 构建时使用 | 说明                         | 最后修改时间 |
| --------------------------- | ------------------------ | ---- | ------ | ---------------------------------------------------------- | ---------- | ---------------------------- | ------------ |
| USER_INFO_API_BASE_URL | 用户信息接口URL             | ✓   |        | https://tsinghuadev.doocom.cn/user-info_api          |            | 具体生产配置地址请找运维咨询 |              |
| BDC_ARCH_API_BASE_URL       | 统一数据接口URL          | ✓   |        | https://tsinghuadev.doocom.cn/bdc-arch-api                 |            | 具体生产配置地址请找运维咨询 |              |
| BDC_AUTH_API_BASE_URL       | 统一权限接口URL          | ✓   |        | https://tsinghuadev.doocom.cn/bdc-auth-api                 |            | 具体生产配置地址请找运维咨询 |              |
| BDC_CORE_API_BASE_URL       | 公共接口 URL             | ✓   |        | https://tsinghuadev.doocom.cn/bdc_core                     |            | 具体生产配置地址请找运维咨询 |              |
| STATIC_RESOURCES_BASE_URL   | 静态资源 URL             | ✓   |        | https://tsinghuadev.doocom.cn/static-resources             | ✓         | 具体生产配置地址请找运维咨询 |              |
| REMOTE_UI_ENTRY_URL         | 远程 UI 入口文件 URL     | ✓   |        | https://tsinghuadev.doocom.cn/remote-ui/remoteEntry.js     | ✓         | 具体生产配置地址请找运维咨询 |              |
| REMOTE_PEOPLE_ENTRY_URL     | 远程选人组件入口文件 URL | ✓   |        | https://tsinghuadev.doocom.cn/remote-people/remoteEntry.js | ✓         | 具体生产配置地址请找运维咨询 |              |
| BDC_SERVICE_API_BASE_URL    | 统一应用接口 URL         | ✓   |        | https://tsinghuadev.doocom.cn/bdc-service_api              | ✓         | 具体生产配置地址请找运维咨询 |              |
| STATIC_RESOURCE_BASE_URL    | 静态资源 URL             | ✓   |        | https://tsinghuadev.doocom.cn/static-resources             | ✓         | 具体生产配置地址请找运维咨询 |              |
| BDC_DFS_API_BASE_URL    | 附件管理 API             | ✓   |        | https://tsinghuadev.doocom.cn/bdc-dfs_api             | ✓         | 具体生产配置地址请找运维咨询 |              |
| USER_INFO_BASE_URL    | 用户信息 API             | ✓   |        | https://tsinghuadev.doocom.cn/user-info_api             | ✓         | 具体生产配置地址请找运维咨询 |              |
| BDC_EXPORT_API_BASE_URL    | 导出组件接口 URL             | ✓   |        | https://tsinghuadev.doocom.cn/bdc-export_api             | ✓         | 具体生产配置地址请找运维咨询 |              |
| BDC_IMPORT_API_BASE_URL    | 导入组件接口 URL             | ✓   |        | https://tsinghuadev.doocom.cn/bdc-import_api             | ✓         | 具体生产配置地址请找运维咨询 |              |

#### CAS相关配置


| 键值         | 备注         | 必填 | 默认值 | 示例                       | 构建时使用 | 说明                         | 最后修改时间 |
| ------------ | ------------ | ---- | ------ | -------------------------- | ---------- | ---------------------------- | ------------ |
| CAS_BASE_URL | CAS 服务 URL | ✓   |        | https://pima.doocom.cn/cas |            | 具体生产配置地址请找运维咨询 |              |
| CAS_VERSION  | CAS 版本     | ✓   |        | 5.2.3                      |            | 预留                         |              |

#### Redis相关配置


| 键值             | 备注                      | 必填 | 默认值                 | 示例                              | 构建时使用 | 说明                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | 最后修改时间 |
| ---------------- | ------------------------- | ---- | ---------------------- | --------------------------------- | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------ |
| REDIS_CLUSTER    | 是否开启 Redis Cluster    |      | off                    | on                                |            | 值必须是 on 才开启                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |              |
| REDIS_URL        | Redis 节点连接 URL        | ✓   |                        | redis://root:123456@********:6379 |            | 当开启 Redis Cluster 时，支持多个 Redis 节点，使用英文逗号分隔如：REDIS_URL=redis://root:123456@********:6379, redis://root:123456@********:6379, redis://root:123456@10.0.0.3:6379当不开启 Redis Cluster 时，直接配置连接 URL如：REDIS_URL=redis://root:123456@********:6379具体生产配置地址请找运维咨询                                                                                                                                                                                                                                   |              |
| REDIS_PASSWORD   | Redis 连接密码            |      |                        |                                   |            | 当开启 Redis Cluster 时，节点连接会 fallback 密码至此密码                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |              |
| REDIS_NAT_MAP    | Redis Cluster NAT Mapping |      |                        | ********:30001=>************:6379 |            | Sometimes the cluster is hosted within a internal networkthat can only be accessed via a NAT (Network Address Translation) instance.Link: https://github.com/luin/ioredis#nat-mapping当开启 Redis Cluster 时，同时存在内外网映射，此时需要配置该项输入格式为规定格式：内网IP:端口号=>外网IP/域:端口号，支持多个映射，使用英文逗号分隔********:30001=>************:6379********:30001=>external-host-1.io:6379如：REDIS_NAT_MAP=********:30001=>external-host-1.io:6379, ********:30002=>external-host-2.io:6379具体生产配置地址请找运维咨询 |              |
| REDIS_KEY_PREFIX | Redis 存储时的键值前缀    |      | pima:project-template: | pima:project-template:            |            | 用于为所有已用键添加前缀的字符串，避免与其他应用重复                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |              |

## 主要使用技术框架

- [webpack](https://webpack.js.org/)
- [Babel](https://babeljs.io/)
- [Express](http://expressjs.com/)
- [Vue.js](https://vuejs.org/)
- [vue-router](https://router.vuejs.org/)
- [vue-server-renderer](https://ssr.vuejs.org/)
- [vue-i18n](https://kazupon.github.io/vue-i18n/)
