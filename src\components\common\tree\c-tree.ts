import { defineComponent, watch, h } from 'vue';
import { Tree, Poptip, DropdownMenu, DropdownItem } from 'view-ui-plus';
import { forestNodes, injectNodesProps } from '@/utils/tree';


function titleRender({ data }) {
  return h('span', { class: 'title', title: data.title }, data.title);
}

function makeExtraElement(vm, { data }) {
  if (!data.extra
    || (Array.isArray(data.extra) && data.extra.length === 0)) {
    return [];
  }

  return [
    h(
      'span',
      { class: 'extra' },
      // eslint-disable-next-line no-underscore-dangle
      vm.extraContentRender(vm, { data }),
    ),
  ];
}

function makeNodeElement(vm, { data }) {
  // eslint-disable-next-line no-use-before-define, @typescript-eslint/no-use-before-define
  const children = makeNodeElementList(vm, data.children);
  return {
    ...data,
    render() {
      return h(
        'span',
        {
          class: 'title-wrap',
        },
        [
          h('span', { class: 'arrow' }),
          vm.titleRender({ data }),
          ...makeExtraElement(vm, { data }),
        ],
      );
    },
    children,
  };
}

function makeNodeElementList(vm, children) {
  if (!Array.isArray(children)) {
    return [];
  }

  const visibleChildren = children.filter(({ visible }) => visible);
  return visibleChildren.map((data) => {
    return makeNodeElement(vm, { data });
  });
}


const CDropdownItem = {
  name: 'CDropdownItem',
  extends: DropdownItem,
  methods: {
    handleClick(event) {
      this.$emit('select', event);
    },
  },
};

function extraPoptipItemsRender(vm, { data }) {
  return [
    h(
      Poptip,
      {
        transfer: true,
        trigger: 'hover',
        placement: 'right-start',
        offset: -10,
        popperClass: 'pima-poptip-transfer',
        width: '50px',
      },
      {
        default() {
          return h('span', { class: 'more' }, [h('i')]);
        },
        content() {
          if (!Array.isArray(data.extra)) {
            return '';
          }

          return h(
            DropdownMenu,
            { class: 'pima-dropdown-menu' },
            () => data.extra.map((item, index) => {
              return h(
                CDropdownItem,
                {
                  onSelect(event) {
                    event.stopPropagation();
                    vm.$emit('click-extra-item', {
                      index,
                      data,
                    });
                  },
                },
                () => item,
              );
            }),
          );
        },
      },
    ),
  ];
}

function expand() {
  if (Array.isArray(this.data) && this.data.length > 0) {
    Object.assign(this.data[0], {
      expand: true,
    });
  }
}

function closeChildrenExpand(node) {
  return node.children.map((ele) => {
    return {
      ...ele,
      expand: false,
      children: closeChildrenExpand(ele),
    };
  });
}


export default defineComponent({
  name: 'CTree',

  props: {
    data: {
      type: Array,
      default() {
        return [];
      },
    },

    titleRender: {
      type: Function,
      default: titleRender,
    },

    extra: {
      type: [String, Object, Array],
      default: undefined,
    },

    extraContentRender: {
      type: Function,
      default: extraPoptipItemsRender,
    },

    expand: {
      type: Function,
      default: expand,
    },

    uniqueKey: {
      type: String,
      default: 'id',
    },
  },

  emits: [
    'click-extra-item',
    'on-toggle-expand',
  ],

  setup(props) {
    function init(value) {
      if (!Array.isArray(value)) {
        return;
      }

      if (props.extra) {
        injectNodesProps(value, { extra: props.extra });
      }
    }

    watch(
      () => props.data,
      (value) => {
        init(value);
        props.expand();
      },
      {
        immediate: true,
        deep: true,
      },
    );

    watch(
      () => props.data,
      (value) => {
        init(value);
      },
      {
        deep: true,
      },
    );
  },

  render() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this;
    return h(
      Tree,
      {
        class: 'pima-tree',
        ...this.$attrs,
        data: makeNodeElementList(that, this.data),
        onOnToggleExpand(currNode) {
          // eslint-disable-next-line no-restricted-syntax
          for (const node of forestNodes(that.data)) {
            if (currNode[that.uniqueKey] === node[that.uniqueKey]) {
              node.expand = !node.expand;
              // 递归调用它的子节点 关闭
              if (node.expand === false) {
                node.children = closeChildrenExpand(node);
              }
            }
          }

          that.$emit('on-toggle-expand', currNode);
        },
        onClickExtraItem({ index, data }) {
          that.$emit('click-extra-item', {
            index,
            data,
          });
        },
      },
    );
  },
});
