<script setup lang="ts">
import { ref, toRefs, unref, computed, watch, nextTick } from 'vue';
import _ from 'lodash';
import { Checkbox } from 'view-ui-plus';
import CTable from '../c-table';
import { theUse } from './the-use';


interface GetRowIdFunction {
  (row: { id: number }): number;
}

interface GetRowCanSelectFunction {
  (row: { id: number }): boolean;
}

interface GetRowClassNameFunction {
  (row: { id: number }): string;
}

interface Props {
  columns: Array<unknown>;
  data: Array<unknown>;
  loading: boolean;
  rowId?: GetRowIdFunction, // 获取某行数据的唯一标识
  rowCanSelect?: GetRowCanSelectFunction, // 获取某行数据是否可以选择，返回false时，显示禁用状态
  rowClassName?: GetRowClassNameFunction, // 获取某行数据的类名
  selected: Array<unknown>, // 选中的原始数据列表
  isCheckedAll?: boolean, // 是否启用全表选中
}

const props = withDefaults(defineProps<Props>(), {
  columns() {
    return [];
  },
  data() {
    return [];
  },
  loading: false,
  rowId() {
    return function getId(o) {
      return o.id;
    };
  },
  rowCanSelect() {
    return function canSelect() {
      return true;
    };
  },
  rowClassName() {
    return function className() {
      return '';
    };
  },
  selected() {
    return [];
  },
  isCheckedAll: false,
});


const emit = defineEmits([
  'update:selected',
  'update:isCheckedAll',
]);


const { columns, data, loading, rowId, rowCanSelect, rowClassName, selected, isCheckedAll } = toRefs(props);
const reRenderHeaderCheckBox = ref(false);


function onUpdateSelected(list) {
  emit('update:selected', list);
}

const { isSelected, addSelected, removeSelected, replaceSelected } = theUse({
  getId: rowId,
  onUpdate: onUpdateSelected,
});

const filterData = computed(() => {
  return _.filter(data.value, rowCanSelect.value);
});

const disableHeaderCheckBox = computed(() => {
  return _.isEmpty(filterData.value) || unref(isCheckedAll);
});

const isAllSelected = computed(() => {
  if (_.isEmpty(data.value)) {
    return false;
  }

  return isCheckedAll.value || data.value.every((o) => isSelected(o));
});


const isIndeterminate = computed(() => {
  if (isAllSelected.value) {
    return false;
  }

  return _.some(data.value, isSelected);
});


watch(
  selected,
  (val) => {
    replaceSelected(val);
  },
  { immediate: true, deep: true },
);


function onTableCheckedChange() {
  reRenderHeaderCheckBox.value = true;

  if (isAllSelected.value || filterData.value.every(isSelected)) {
    removeSelected(...filterData.value);
  } else {
    addSelected(...filterData.value);
  }

  nextTick(() => {
    reRenderHeaderCheckBox.value = false;
  });
}

function onCheckedChange(ev, row) {
  if (ev.target.checked) {
    addSelected(row);
  } else {
    removeSelected(row);
  }
}

const tableColumns = computed(() => {
  if (_.isEmpty(unref(columns))) {
    return [];
  }

  const columnCheck = {
    // slot: '__CHECK__',
    key: '__CHECK__',
    width: 35,
    renderHeader(h) {
      // 头部的CheckBox渲染有问题，某些时候必须强制重新渲染
      if (loading.value || reRenderHeaderCheckBox.value) {
        return '';
      }

      return h(Checkbox, {
        class: 'pima-checkbox',
        disabled: disableHeaderCheckBox.value,
        modelValue: isAllSelected.value,
        indeterminate: isIndeterminate.value,
        onChange: onTableCheckedChange,
      });
    },
    render: (h, { row }) => {
      return h(Checkbox, {
        class: 'pima-checkbox',
        modelValue: isSelected(row) || isCheckedAll.value,
        disabled: !rowCanSelect.value(row) || isCheckedAll.value,
        onChange(v) {
          onCheckedChange(v, row);
        },
      });
    },
  };

  return [
    columnCheck,
    ...unref(columns),
  ];
});
</script>


<template>
  <CTable
    class="small-padding table-selectable"
    :columns="tableColumns"
    :data="data"
    :loading="loading"
    :row-class-name="rowClassName"
  >
    <template
      v-for="(slot, slotName) in $slots"
      #[slotName]="slotProps"
    >
      <slot
        :name="slotName"
        v-bind="slotProps ?? {}"
      />
    </template>
  </CTable>
</template>
