import { defineComponent, getCurrentInstance, watch, nextTick, h } from 'vue';
import _ from 'lodash';
import { forestNodes, injectNodesProps } from '@/utils/tree';
import CTree from './c-tree';


function titleRender({ data }) {
  if (this.keyword) {
    let title = data.title || '';
    const keyword = this.keyword.toLowerCase();
    if (title.includes(keyword)) {
      const children = [];
      while (title.length > 0) {
        const endIdx = title.toLowerCase().indexOf(keyword);
        if (endIdx > 0) {
          const str = title.substring(0, endIdx);
          children.push(h('span', {}, str));
          title = title.slice(endIdx);
        } else if (endIdx === 0) {
          const str = title.substring(0, keyword.length);
          children.push(h('span', { class: 'highlight' }, str));
          title = title.slice(keyword.length);
        } else {
          const str = title;
          children.push(h('span', {}, str));
          title = '';
        }
      }

      return h('span', { class: 'title', title: data.title }, children);
    }
  }

  return h('span', { class: 'title', title: data.title }, data.title);
}

function keywordMatch(data, keyword) {
  return data.title.toLowerCase().includes(keyword);
}

function hasMatchChildren(data, keyword) {
  if (!Array.isArray(data)) {
    return false;
  }

  let has = false;
  data.forEach((item) => {
    if (!has) {
      if (keywordMatch(item, keyword)) {
        has = true;
      } else {
        has = hasMatchChildren(item.children, keyword);
      }
    }
  });

  return has;
}

function filterDeep(data, keyword) {
  if (!Array.isArray(data)) {
    return;
  }

  data.forEach((item) => {
    const visible = keywordMatch(item, keyword) || hasMatchChildren(item.children, keyword);
    Object.assign(item, {
      visible,
    });
    filterDeep(item.children, keyword);
  });
}

function expandMatch(data, selectedKeys, uniqueKey) {
  return selectedKeys.includes(data[uniqueKey]);
}

function hasExpandChildren(data, selectedKeys, uniqueKey) {
  if (!Array.isArray(data)) {
    return false;
  }

  let has = false;
  data.forEach((item) => {
    if (!has) {
      if (expandMatch(item, selectedKeys, uniqueKey)) {
        has = true;
      } else {
        has = hasExpandChildren(item.children, selectedKeys, uniqueKey);
      }
    }
  });

  return has;
}

function expandKeys(data, selectedKeys, uniqueKey) {
  if (!Array.isArray(data)) {
    return [];
  }

  const nodeKeys = [];
  data.forEach((item) => {
    const isExpanded = expandMatch(item, selectedKeys, uniqueKey)
      || hasExpandChildren(item.children, selectedKeys, uniqueKey);
    if (isExpanded) {
      nodeKeys.push(item[uniqueKey]);
    }

    nodeKeys.push(...expandKeys(item.children, selectedKeys, uniqueKey));
  });

  return nodeKeys;
}

function expand() {
  if (Array.isArray(this.data) && this.data.length > 0) {
    Object.assign(this.data[0], {
      expand: true,
    });
  }
}

function expandVisableNodes() {
  const selectedNodes = [];
  // eslint-disable-next-line no-restricted-syntax
  for (const node of forestNodes(this.data)) {
    if (node.selected) {
      selectedNodes.push(node);
    }
  }

  if (selectedNodes.length === 0 && this.keyword.length === 0) {
    // eslint-disable-next-line no-restricted-syntax
    for (const node of forestNodes(this.data)) {
      if (node.visible) {
        node.expand = false;
      }
    }

    this.expand();
  } else if (selectedNodes.length === 0 && this.keyword.length > 0) {
    // eslint-disable-next-line no-restricted-syntax
    for (const node of forestNodes(this.data)) {
      if (node.visible) {
        node.expand = true;
      }
    }
  } else if (selectedNodes.length > 0 && this.keyword.length > 0) {
    // eslint-disable-next-line no-restricted-syntax
    for (const node of forestNodes(this.data)) {
      if (node.visible) {
        node.expand = true;
      }
    }
  } else {
    const selectedKeys = _.map(selectedNodes, this.uniqueKey);
    const distKeys = expandKeys(this.data, selectedKeys, this.uniqueKey);
    // eslint-disable-next-line no-restricted-syntax
    for (const node of forestNodes(this.data)) {
      node.expand = distKeys.includes(node[this.uniqueKey]);
    }
  }
}

function filter() {
  filterDeep(this.data, this.keyword.toLowerCase());

  const expandVisableNodesFn = expandVisableNodes.bind(this);
  expandVisableNodesFn(this.data, this.keyword.toLowerCase(), this.uniqueKey, this.expand);
}

export default defineComponent({
  name: 'CTreeFilter',
  extends: CTree,
  props: {
    titleRender: {
      type: Function,
      default: titleRender,
    },

    keyword: {
      type: String,
      default: '',
    },

    filter: {
      type: Function,
      default: filter,
    },

    wait: {
      type: Number,
      default: 500,
    },

    expand: {
      type: Function,
      default: expand,
    },

    uniqueKey: {
      type: String,
      default: 'id',
    },
  },

  setup(props) {
    const vm = getCurrentInstance();

    function init(value) {
      if (!Array.isArray(value)) {
        return;
      }

      if (props.extra) {
        injectNodesProps(value, { extra: props.extra });
      }
    }

    watch(
      () => props.data,
      (value) => {
        init(value);
        props.expand();
      },
      {
        immediate: true,
      },
    );

    watch(
      () => props.data,
      (value) => {
        init(value);
      },
      {
        deep: true,
      },
    );


    const filterFn = props.filter.bind(vm.proxy);
    const filterDeb = _.debounce(filterFn, props.wait);
    watch(
      () => props.keyword,
      () => {
        nextTick(() => {
          filterDeb();
        });
      },
    );
  },
});
