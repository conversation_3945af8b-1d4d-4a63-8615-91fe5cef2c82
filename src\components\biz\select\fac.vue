<script setup lang="ts">
import { onBeforeMount } from 'vue';

import { useFacCls } from '@/uses/use-fac-cls';


const { fetchFac, loadingFac, facList } = useFacCls();


onBeforeMount(() => {
  fetchFac();
});

</script>


<template>
  <Select
    class="pima-select"
    :loading="loadingFac"
    v-bind="$attrs"
  >
    <Option
      v-for="item in facList"
      :key="item.id"
      :value="item.id"
      :label="item.name"
    />
  </Select>
</template>
