import type { ColumnsType } from '^/types/columns';

import { namespaceT } from '@/helps/namespace-t';

export const createColumns = (): ColumnsType[] => {
  const t = namespaceT('jobPost.columns');
  const tc = namespaceT('common.table');

  return [
    // 岗位名称
    {
      title: t('postName'),
      key: 'postName',
      minWidth: 100,
    },

    // 研究方向
    {
      title: t('researchDirection'),
      key: 'researchDirection',
      minWidth: 100,
    },

    // 性别要求
    {
      title: t('sexRemand'),
      key: 'sexRemand',
      minWidth: 100,
    },

    // 所需专业
    {
      title: t('neededMajor'),
      key: 'neededMajor',
      minWidth: 100,
    },

    // 需求人数
    {
      title: t('numOfPerson'),
      key: 'numOfPerson',
      width: 80,
    },

    // 实践地址
    {
      title: t('practiceAddress'),
      key: 'practiceAddress',
      minWidth: 100,
    },

    // 报名截止时间
    {
      title: t('deadline'),
      key: 'deadline',
      minWidth: 100,
    },


    // 创建时间
    {
      title: t('createTime'),
      slot: 'createTime',
      minWidth: 100,
    },

    // 状态
    {
      title: t('status'),
      key: 'status',
      width: 100,
    },

    // 发布状态/发布时间
    {
      title: t('publishStatusAndTime'),
      slot: 'publishStatusAndTime',
      minWidth: 100,
    },

    // 操作
    {
      title: tc('operation'),
      slot: 'operation',
      width: 180,
    },
  ];
};
